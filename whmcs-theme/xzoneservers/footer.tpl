                </div>

                <!-- Sidebar -->
                <div id="sidebar" class="w-full lg:w-1/4 px-4">
                    {include file="$template/includes/sidebar.tpl"}
                </div>
            </div>
        </div>
    </main>

    <!-- Enhanced Footer -->
    <footer id="footer" class="relative bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 border-t border-slate-800/50 mt-16">
        <!-- Background Pattern -->
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23334155" fill-opacity="0.03"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>

        <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
                <!-- Brand Section -->
                <div class="lg:col-span-1">
                    <a href="{$systemurl}" class="text-2xl font-bold text-white flex items-center group mb-6">
                        {if $logo}
                            <img src="{$logo}" alt="{$companyname}" class="h-8 w-auto mr-3">
                        {/if}
                        <span class="bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
                            {$companyname}
                        </span>
                    </a>
                    <p class="text-gray-400 text-sm leading-relaxed mb-6">
                        Enterprise-grade hosting solutions with AI-optimized global network infrastructure, delivering unmatched performance and reliability worldwide.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-slate-800/50 border border-slate-700/50 rounded-lg flex items-center justify-center text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/20 hover:to-purple-500/20 hover:border-blue-500/30 transition-all duration-300">
                            <i data-lucide="twitter" class="w-4 h-4"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-slate-800/50 border border-slate-700/50 rounded-lg flex items-center justify-center text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/20 hover:to-purple-500/20 hover:border-blue-500/30 transition-all duration-300">
                            <i data-lucide="linkedin" class="w-4 h-4"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-slate-800/50 border border-slate-700/50 rounded-lg flex items-center justify-center text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/20 hover:to-purple-500/20 hover:border-blue-500/30 transition-all duration-300">
                            <i data-lucide="github" class="w-4 h-4"></i>
                        </a>
                    </div>
                </div>

                <!-- Services Section -->
                <div>
                    <h4 class="font-semibold text-white mb-6 flex items-center">
                        <i data-lucide="server" class="w-4 h-4 mr-2 text-blue-400"></i>
                        Services
                    </h4>
                    <ul class="space-y-3 text-gray-400">
                        <li><a href="{$systemurl}/cart.php" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="shopping-cart" class="w-3 h-3 mr-2"></i>Order Services</a></li>
                        <li><a href="{$systemurl}/clientarea.php?action=services" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="database" class="w-3 h-3 mr-2"></i>My Services</a></li>
                        <li><a href="{$systemurl}/clientarea.php?action=domains" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="globe" class="w-3 h-3 mr-2"></i>Domain Management</a></li>
                        <li><a href="{$systemurl}/clientarea.php?action=invoices" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="file-text" class="w-3 h-3 mr-2"></i>Invoices</a></li>
                    </ul>
                </div>

                <!-- Account Section -->
                <div>
                    <h4 class="font-semibold text-white mb-6 flex items-center">
                        <i data-lucide="user" class="w-4 h-4 mr-2 text-purple-400"></i>
                        Account
                    </h4>
                    <ul class="space-y-3 text-gray-400">
                        {if $loggedin}
                            <li><a href="{$systemurl}/clientarea.php" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="layout-dashboard" class="w-3 h-3 mr-2"></i>Dashboard</a></li>
                            <li><a href="{$systemurl}/clientarea.php?action=details" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="settings" class="w-3 h-3 mr-2"></i>Account Settings</a></li>
                            <li><a href="{$systemurl}/clientarea.php?action=emails" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="mail" class="w-3 h-3 mr-2"></i>Email History</a></li>
                            <li><a href="{$systemurl}/logout.php" class="hover:text-white hover:bg-gradient-to-r hover:from-red-500/10 hover:to-orange-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="log-out" class="w-3 h-3 mr-2"></i>Logout</a></li>
                        {else}
                            <li><a href="{$systemurl}/login.php" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="log-in" class="w-3 h-3 mr-2"></i>Login</a></li>
                            <li><a href="{$systemurl}/register.php" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="user-plus" class="w-3 h-3 mr-2"></i>Register</a></li>
                            <li><a href="{$systemurl}/pwreset.php" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="key" class="w-3 h-3 mr-2"></i>Reset Password</a></li>
                        {/if}
                    </ul>
                </div>

                <!-- Support Section -->
                <div>
                    <h4 class="font-semibold text-white mb-6 flex items-center">
                        <i data-lucide="life-buoy" class="w-4 h-4 mr-2 text-green-400"></i>
                        Support
                    </h4>
                    <ul class="space-y-3 text-gray-400">
                        <li><a href="{$systemurl}/knowledgebase.php" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="book-open" class="w-3 h-3 mr-2"></i>Knowledge Base</a></li>
                        <li><a href="{$systemurl}/submitticket.php" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="ticket" class="w-3 h-3 mr-2"></i>Submit Ticket</a></li>
                        <li><a href="{$systemurl}/supporttickets.php" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="inbox" class="w-3 h-3 mr-2"></i>View Tickets</a></li>
                        <li><a href="{$systemurl}/serverstatus.php" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="activity" class="w-3 h-3 mr-2"></i>System Status</a></li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Section -->
            <div class="mt-16 pt-8 border-t border-slate-800/50">
                <div class="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
                    <div class="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6 text-sm text-gray-500">
                        <p>&copy; {$date_year} {$companyname}. All rights reserved.</p>
                        <div class="flex space-x-6">
                            <a href="#" class="hover:text-gray-300 transition-colors duration-300">Privacy Policy</a>
                            <a href="#" class="hover:text-gray-300 transition-colors duration-300">Terms of Service</a>
                            <a href="#" class="hover:text-gray-300 transition-colors duration-300">Cookie Policy</a>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                            <span>All Systems Operational</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript for Mobile Menu -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const hamburgerLines = document.querySelectorAll('.hamburger-line');

            if (mobileMenuButton && mobileMenu) {
                let isOpen = false;

                mobileMenuButton.addEventListener('click', function() {
                    isOpen = !isOpen;
                    mobileMenu.classList.toggle('hidden');

                    // Animate hamburger to X
                    if (isOpen) {
                        hamburgerLines[0].style.transform = 'rotate(45deg) translate(6px, 6px)';
                        hamburgerLines[1].style.opacity = '0';
                        hamburgerLines[2].style.transform = 'rotate(-45deg) translate(6px, -6px)';
                    } else {
                        hamburgerLines[0].style.transform = 'rotate(0) translate(0, 0)';
                        hamburgerLines[1].style.opacity = '1';
                        hamburgerLines[2].style.transform = 'rotate(0) translate(0, 0)';
                    }
                });

                // Close mobile menu when clicking outside
                document.addEventListener('click', function(e) {
                    if (!mobileMenuButton.contains(e.target) && !mobileMenu.contains(e.target) && !mobileMenu.classList.contains('hidden')) {
                        isOpen = false;
                        mobileMenu.classList.add('hidden');
                        hamburgerLines[0].style.transform = 'rotate(0) translate(0, 0)';
                        hamburgerLines[1].style.opacity = '1';
                        hamburgerLines[2].style.transform = 'rotate(0) translate(0, 0)';
                    }
                });
            }

            // Initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });
    </script>

    <!-- WHMCS Required Footer JavaScript -->
    {$footeroutput}
</body>
</html>