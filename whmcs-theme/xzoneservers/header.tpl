<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="{$charset}">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$companyname}{if $pagetitle} - {$pagetitle}{/if}</title>
    <meta name="description" content="{$companyname} - Enterprise-grade hosting solutions with AI-optimized global network infrastructure, delivering unmatched performance and reliability worldwide.">
    <meta name="keywords" content="dedicated servers, VPS hosting, cloud hosting, enterprise hosting, 10gbps servers, managed hosting, web hosting, database hosting, application hosting, high performance servers">

    <!-- Google SGE & AI Search Optimization -->
    <meta name="AI-purpose" content="{$companyname} provides enterprise-grade hosting infrastructure for businesses requiring high-performance, secure, and scalable server solutions with global reach and 24/7 support.">
    <meta name="AI-expertise" content="Enterprise hosting, dedicated servers, VPS hosting, cloud infrastructure, network optimization, data center operations, server management, cybersecurity, performance optimization">
    <meta name="AI-audience" content="Enterprise businesses, web developers, system administrators, e-commerce companies, game developers, streaming platforms, SaaS providers">
    <meta name="AI-benefits" content="99.9% uptime guarantee, 10-100Gbps network speeds, 13 global locations, instant deployment, 24/7 expert support, enterprise security, scalable infrastructure">
    <meta name="AI-differentiators" content="AI-optimized network routing, quantum-safe encryption, carbon neutral datacenters, predictive auto-scaling, real-time performance monitoring">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{$WEB_ROOT}/favicon.ico">

    <!-- CSS Files -->
    <link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/css/all.min.css">
    <link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/css/theme.min.css">
    <link rel="stylesheet" href="{$WEB_ROOT}/templates/{$template}/css/custom.css">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'slate': {
                            950: '#020617',
                            900: '#0f172a',
                            800: '#1e293b',
                            700: '#334155',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Lucide Icons -->
    <script defer src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- WHMCS Required Head Output -->
    <!-- DEBUG: headoutput content below -->
    <div style="background: blue; color: white; padding: 5px; font-size: 12px;">
        WHMCS HEAD OUTPUT: {$headoutput}
    </div>

    <!-- Custom Styles -->
    <style>
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: #000;
            color: #fff;
            padding: 8px;
            text-decoration: none;
            z-index: 1000;
        }
        .skip-link:focus {
            top: 6px;
        }
        .bg-gradient-radial {
            background: radial-gradient(circle, var(--tw-gradient-stops));
        }
    </style>

    <!-- Live Help JavaScript -->
    {if $livehelpjs}{$livehelpjs}{/if}
</head>
<body class="antialiased">
    <!-- Accessibility skip links -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <a href="#footer" class="skip-link">Skip to footer</a>

    <!-- Enhanced Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-slate-950/95 via-slate-900/95 to-slate-950/95 backdrop-blur-xl border-b border-slate-800/50">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-20">
                <!-- Logo -->
                <a href="{$systemurl}" class="text-2xl font-bold text-white flex items-center group">
                    {if $logo}
                        <img src="{$logo}" alt="{$companyname}" class="h-8 w-auto mr-3">
                    {/if}
                    <span class="bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
                        {$companyname}
                    </span>
                </a>

                <!-- Desktop Navigation -->
                <nav class="hidden xl:flex items-center space-x-1">
                    <a href="{$systemurl}" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link">
                        <span class="relative z-10 flex items-center">
                            <i data-lucide="home" class="w-4 h-4 mr-2"></i>
                            Home
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>

                    <div class="relative group">
                        <button class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link flex items-center">
                            <span class="relative z-10 flex items-center">
                                <i data-lucide="server" class="w-4 h-4 mr-2"></i>
                                Services
                                <i data-lucide="chevron-down" class="w-3 h-3 ml-1 transition-transform duration-300 group-hover:rotate-180"></i>
                            </span>
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </button>
                        <!-- Services Dropdown -->
                        <div class="absolute top-full left-0 mt-2 w-48 bg-slate-900/95 backdrop-blur-xl border border-slate-700/50 rounded-xl shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-2">
                                <a href="{$systemurl}/cart.php" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                                    <i data-lucide="shopping-cart" class="w-4 h-4 mr-3"></i>
                                    Order Services
                                </a>
                                <a href="{$systemurl}/clientarea.php?action=services" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 rounded-lg transition-all duration-300">
                                    <i data-lucide="database" class="w-4 h-4 mr-3"></i>
                                    My Services
                                </a>
                            </div>
                        </div>
                    </div>

                    <a href="{$systemurl}/clientarea.php?action=domains" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link">
                        <span class="relative z-10 flex items-center">
                            <i data-lucide="globe" class="w-4 h-4 mr-2"></i>
                            Domains
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>

                    <a href="{$systemurl}/submitticket.php" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link">
                        <span class="relative z-10 flex items-center">
                            <i data-lucide="life-buoy" class="w-4 h-4 mr-2"></i>
                            Support
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>

                    <a href="{$systemurl}/knowledgebase.php" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link">
                        <span class="relative z-10 flex items-center">
                            <i data-lucide="book-open" class="w-4 h-4 mr-2"></i>
                            Knowledge Base
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-orange-500/10 to-red-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>
                </nav>

                <!-- User Account Section -->
                <div class="hidden xl:flex items-center space-x-4">
                    {if $loggedin}
                        <div class="relative group">
                            <button class="flex items-center px-4 py-2 text-gray-300 hover:text-white transition-all duration-300">
                                <i data-lucide="user" class="w-4 h-4 mr-2"></i>
                                {$client.firstname} {$client.lastname}
                                <i data-lucide="chevron-down" class="w-3 h-3 ml-1 transition-transform duration-300 group-hover:rotate-180"></i>
                            </button>
                            <!-- User Dropdown -->
                            <div class="absolute top-full right-0 mt-2 w-48 bg-slate-900/95 backdrop-blur-xl border border-slate-700/50 rounded-xl shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="p-2">
                                    <a href="{$systemurl}/clientarea.php" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                                        <i data-lucide="layout-dashboard" class="w-4 h-4 mr-3"></i>
                                        Dashboard
                                    </a>
                                    <a href="{$systemurl}/clientarea.php?action=details" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 rounded-lg transition-all duration-300">
                                        <i data-lucide="settings" class="w-4 h-4 mr-3"></i>
                                        Account Settings
                                    </a>
                                    <a href="{$systemurl}/logout.php" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-red-500/10 hover:to-orange-500/10 rounded-lg transition-all duration-300">
                                        <i data-lucide="log-out" class="w-4 h-4 mr-3"></i>
                                        Logout
                                    </a>
                                </div>
                            </div>
                        </div>
                    {else}
                        <a href="{$systemurl}/login.php" class="px-4 py-2 text-gray-300 hover:text-white transition-all duration-300">
                            <i data-lucide="log-in" class="w-4 h-4 mr-2 inline"></i>
                            Login
                        </a>
                        <a href="{$systemurl}/register.php" class="inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-105">
                            <i data-lucide="user-plus" class="w-4 h-4 mr-2"></i>
                            Register
                        </a>
                    {/if}
                </div>

                <!-- Mobile Menu Button -->
                <button id="mobile-menu-button" class="xl:hidden p-2 text-white hover:bg-slate-800/50 rounded-lg transition-colors duration-300 relative">
                    <div class="w-6 h-6 flex flex-col justify-center items-center">
                        <span class="hamburger-line block w-5 h-0.5 bg-white transition-all duration-300 ease-in-out"></span>
                        <span class="hamburger-line block w-5 h-0.5 bg-white mt-1 transition-all duration-300 ease-in-out"></span>
                        <span class="hamburger-line block w-5 h-0.5 bg-white mt-1 transition-all duration-300 ease-in-out"></span>
                    </div>
                </button>
            </div>
        </div>

        <!-- Enhanced Mobile Menu -->
        <div id="mobile-menu" class="hidden xl:hidden bg-gradient-to-br from-slate-950/98 to-slate-900/98 backdrop-blur-xl border-t border-slate-800/50">
            <div class="px-4 py-6 space-y-3">
                <a href="{$systemurl}" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-xl transition-all duration-300">
                    <i data-lucide="home" class="w-5 h-5 mr-3"></i>
                    Home
                </a>
                <a href="{$systemurl}/cart.php" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 rounded-xl transition-all duration-300">
                    <i data-lucide="shopping-cart" class="w-5 h-5 mr-3"></i>
                    Order Services
                </a>
                <a href="{$systemurl}/clientarea.php?action=services" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-orange-500/10 hover:to-red-500/10 rounded-xl transition-all duration-300">
                    <i data-lucide="database" class="w-5 h-5 mr-3"></i>
                    My Services
                </a>
                <a href="{$systemurl}/clientarea.php?action=domains" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-green-500/10 hover:to-blue-500/10 rounded-xl transition-all duration-300">
                    <i data-lucide="globe" class="w-5 h-5 mr-3"></i>
                    Domains
                </a>
                <a href="{$systemurl}/submitticket.php" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-pink-500/10 hover:to-orange-500/10 rounded-xl transition-all duration-300">
                    <i data-lucide="life-buoy" class="w-5 h-5 mr-3"></i>
                    Support
                </a>
                <a href="{$systemurl}/knowledgebase.php" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-teal-500/10 hover:to-cyan-500/10 rounded-xl transition-all duration-300">
                    <i data-lucide="book-open" class="w-5 h-5 mr-3"></i>
                    Knowledge Base
                </a>

                {if $loggedin}
                    <div class="pt-4 border-t border-slate-800/50">
                        <a href="{$systemurl}/clientarea.php" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-xl transition-all duration-300">
                            <i data-lucide="layout-dashboard" class="w-5 h-5 mr-3"></i>
                            Dashboard
                        </a>
                        <a href="{$systemurl}/clientarea.php?action=details" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 rounded-xl transition-all duration-300">
                            <i data-lucide="settings" class="w-5 h-5 mr-3"></i>
                            Account Settings
                        </a>
                        <a href="{$systemurl}/logout.php" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-red-500/10 hover:to-orange-500/10 rounded-xl transition-all duration-300">
                            <i data-lucide="log-out" class="w-5 h-5 mr-3"></i>
                            Logout
                        </a>
                    </div>
                {else}
                    <div class="pt-4 border-t border-slate-800/50 space-y-3">
                        <a href="{$systemurl}/login.php" class="flex items-center justify-center w-full px-6 py-3 bg-slate-800/50 text-white font-semibold rounded-xl hover:bg-slate-700/50 transition-all duration-300">
                            <i data-lucide="log-in" class="w-4 h-4 mr-2"></i>
                            Login
                        </a>
                        <a href="{$systemurl}/register.php" class="flex items-center justify-center w-full px-6 py-3 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300">
                            <i data-lucide="user-plus" class="w-4 h-4 mr-2"></i>
                            Register
                        </a>
                    </div>
                {/if}
            </div>
        </div>
    </header>

    <!-- Main Content Container -->
    <main id="main-content" role="main" class="pt-20">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Full Width Content Area -->
            <div class="w-full">